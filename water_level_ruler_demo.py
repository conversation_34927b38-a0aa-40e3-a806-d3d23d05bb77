#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水位标尺OCR识别Demo
专门针对水位标尺图像的传统图像算法实现
支持识别如376M这样的水位读数
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import re
import os

class WaterLevelRulerOCR:
    """水位标尺专用OCR识别类"""
    
    def __init__(self):
        """初始化"""
        self.ocr = None
        self.ocr_available = False
        self._init_ocr()
    
    def _init_ocr(self):
        """初始化OCR引擎"""
        # 优先尝试PaddleOCR
        try:
            from paddleocr import PaddleOCR
            # 使用新的参数名，避免deprecation warning
            self.ocr = PaddleOCR(use_textline_orientation=True, lang='ch')
            self.ocr_available = True
            print("✓ PaddleOCR初始化成功")
            return
        except ImportError:
            print("× PaddleOCR未安装")
        except Exception as e:
            print(f"× PaddleOCR初始化失败: {e}")
            # 尝试使用旧版本参数
            try:
                self.ocr = PaddleOCR(use_angle_cls=True, lang='ch')
                self.ocr_available = True
                print("✓ PaddleOCR初始化成功 (使用兼容模式)")
                return
            except Exception as e2:
                print(f"× PaddleOCR兼容模式也失败: {e2}")
        
        # 备用方案：Tesseract
        try:
            import pytesseract
            pytesseract.get_tesseract_version()
            self.ocr = pytesseract
            self.ocr_available = True
            print("✓ Tesseract OCR初始化成功")
            return
        except ImportError:
            print("× Tesseract未安装")
        except Exception as e:
            print(f"× Tesseract初始化失败: {e}")
        
        print("⚠ 未找到可用的OCR引擎，将使用基础图像处理")
    
    def preprocess_ruler_image(self, image_path):
        """
        专门针对水位标尺的图像预处理 - 不使用颜色过滤，保留所有文字
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        print(f"原始图像尺寸: {image.shape}")
        
        # 直接返回原始图像，不进行颜色过滤
        # 创建一个全白的掩码，表示使用整个图像
        mask = np.ones(image.shape[:2], dtype=np.uint8) * 255
        
        return image, image.copy(), mask
    
    def extract_ruler_roi(self, image, mask):
        """
        提取标尺的感兴趣区域(ROI) - 简化版本，直接返回整个图像
        """
        # 由于我们不再使用颜色过滤，直接返回整个图像
        print(f"使用整个图像作为ROI: {image.shape}")
        return image
    
    def enhance_text_regions(self, image):
        """
        增强文字区域的可读性 - 最小化处理，保留原始信息
        """
        # 如果是彩色图像，转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 仅做轻微的对比度增强
        # 使用CLAHE但参数更保守
        clahe = cv2.createCLAHE(clipLimit=1.5, tileGridSize=(4, 4))
        enhanced = clahe.apply(gray)
        
        # 返回增强后的图像，不做任何二值化或形态学处理
        return enhanced
    
    def recognize_with_paddleocr(self, image, enhanced_image=None):
        """
        使用PaddleOCR识别文字
        enhanced_image: 增强后的图像，如果提供则优先使用
        """
        if not self.ocr_available or not hasattr(self.ocr, 'ocr'):
            return []
        
        try:
            # 尝试多种图像进行识别
            results_list = []
            
            # 1. 识别原始图像
            temp_path = "temp_ocr_image.jpg"
            cv2.imwrite(temp_path, image)
            result1 = self.ocr.ocr(temp_path, cls=True)
            if result1:
                results_list.append(('原始图像', result1))
            
            # 2. 如果有增强图像，也进行识别
            if enhanced_image is not None:
                # 将灰度图转换为3通道
                if len(enhanced_image.shape) == 2:
                    enhanced_3ch = cv2.cvtColor(enhanced_image, cv2.COLOR_GRAY2BGR)
                else:
                    enhanced_3ch = enhanced_image
                
                cv2.imwrite(temp_path, enhanced_3ch)
                result2 = self.ocr.ocr(temp_path, cls=True)
                if result2:
                    results_list.append(('增强图像', result2))
            
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
            
            # 合并和解析所有结果
            parsed_results = []
            seen_texts = set()  # 避免重复
            
            for source, result in results_list:
                if result and len(result) > 0:
                    for line in result:
                        if line:
                            for item in line:
                                if len(item) >= 2:
                                    bbox = item[0]
                                    text_info = item[1]
                                    
                                    if isinstance(text_info, tuple) and len(text_info) >= 2:
                                        text = text_info[0]
                                        confidence = text_info[1]
                                        
                                        # 避免重复，但保留包含"M"的文本
                                        if text not in seen_texts or 'M' in text.upper():
                                            if confidence > 0.3:  # 降低置信度阈值
                                                parsed_results.append({
                                                    'text': text,
                                                    'confidence': confidence,
                                                    'bbox': bbox,
                                                    'source': source
                                                })
                                                seen_texts.add(text)
            
            # 按置信度排序
            parsed_results.sort(key=lambda x: x['confidence'], reverse=True)
            
            return parsed_results
            
        except Exception as e:
            print(f"PaddleOCR识别出错: {e}")
            return []
    
    def recognize_with_tesseract(self, image):
        """
        使用Tesseract识别文字
        """
        if not self.ocr_available or not hasattr(self.ocr, 'image_to_string'):
            return []
        
        try:
            # 转换为PIL图像
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            
            # 使用tesseract识别
            text = self.ocr.image_to_string(pil_image, lang='chi_sim+eng', 
                                          config='--psm 6 -c tessedit_char_whitelist=0123456789MmCc')
            
            return [{'text': text.strip(), 'confidence': 0.8, 'bbox': None}]
            
        except Exception as e:
            print(f"Tesseract识别出错: {e}")
            return []
    
    def extract_water_level_values(self, ocr_results):
        """
        从OCR结果中提取水位数值
        """
        water_levels = []
        
        for result in ocr_results:
            text = result['text'].strip()
            
            # 定义匹配水位读数的正则表达式
            patterns = [
                r'(\d{3,4})[Mm]',           # 376M, 377M 格式
                r'(\d{3,4})\.(\d+)[Mm]',    # 376.5M 格式  
                r'(\d{3,4})',               # 纯数字 376, 377
                r'(\d{2,3})\.(\d+)',        # 小数格式 76.5
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    if isinstance(match, tuple):
                        # 处理小数格式
                        if len(match) == 2:
                            value = f"{match[0]}.{match[1]}"
                        else:
                            value = match[0]
                    else:
                        value = match
                    
                    # 验证数值合理性（水位通常在100-500米范围）
                    try:
                        num_value = float(value)
                        if 50 <= num_value <= 1000:
                            water_levels.append({
                                'value': value,
                                'unit': 'M' if 'M' in text.upper() or 'm' in text else '',
                                'confidence': result['confidence'],
                                'original_text': text,
                                'bbox': result.get('bbox')
                            })
                    except ValueError:
                        continue
        
        return water_levels
    
    def detect_water_line(self, image):
        """
        检测实际的水位线位置
        """
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 高斯模糊减少噪声
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 使用Canny边缘检测
        edges = cv2.Canny(blurred, 50, 150, apertureSize=3)
        
        # 使用霍夫直线变换检测水平线
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50,
                               minLineLength=30, maxLineGap=10)
        
        water_lines = []
        if lines is not None:
            for line in lines:
                x1, y1, x2, y2 = line[0]
                # 计算直线角度，筛选接近水平的线
                angle = np.abs(np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi)
                if angle < 15 or angle > 165:  # 接近水平的线
                    # 计算直线长度
                    length = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)
                    if length > 20:  # 过滤太短的线段
                        water_lines.append({
                            'line': line[0],
                            'y_pos': (y1 + y2) / 2,  # 水位线的Y坐标
                            'length': length,
                            'angle': angle
                        })
        
        # 按长度排序，选择最长的水平线作为水位线
        if water_lines:
            water_lines.sort(key=lambda x: x['length'], reverse=True)
            return water_lines[0]
        
        return None
    
    def calculate_scale_spacing(self, ocr_results, image_height):
        """
        计算相邻刻度间的像素距离
        """
        # 提取有效的刻度标记位置
        scale_marks = []
        
        for result in ocr_results:
            if result.get('bbox'):
                bbox = result['bbox']
                # 计算bbox的中心Y坐标
                center_y = np.mean([point[1] for point in bbox])
                
                # 尝试从文本中提取数值
                text = result['text'].strip()
                patterns = [
                    r'(\d{3,4})[Mm]',
                    r'(\d{3,4})',
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, text)
                    if matches:
                        try:
                            value = float(matches[0])
                            if 50 <= value <= 1000:
                                scale_marks.append({
                                    'value': value,
                                    'y_pos': center_y,
                                    'text': text
                                })
                                break
                        except ValueError:
                            continue
        
        # 按数值排序
        scale_marks.sort(key=lambda x: x['value'])
        
        # 计算相邻刻度间的像素距离
        pixel_per_unit = None
        if len(scale_marks) >= 2:
            total_pixel_diff = 0
            total_value_diff = 0
            valid_pairs = 0
            
            for i in range(len(scale_marks) - 1):
                value_diff = scale_marks[i+1]['value'] - scale_marks[i]['value']
                pixel_diff = abs(scale_marks[i+1]['y_pos'] - scale_marks[i]['y_pos'])
                
                # 只考虑相邻的整数刻度
                if value_diff == 1.0 and pixel_diff > 0:
                    total_pixel_diff += pixel_diff
                    total_value_diff += value_diff
                    valid_pairs += 1
            
            if valid_pairs > 0:
                pixel_per_unit = total_pixel_diff / total_value_diff
                print(f"计算得到的像素/米比例: {pixel_per_unit:.2f} pixels/meter")
        
        return scale_marks, pixel_per_unit
    
    def calculate_precise_water_level(self, water_line, scale_marks, pixel_per_unit):
        """
        基于水位线位置和刻度信息计算精确水位
        """
        if not water_line or not scale_marks or not pixel_per_unit:
            return None
        
        water_y = water_line['y_pos']
        
        # 找到水位线上下最近的两个刻度
        above_mark = None
        below_mark = None
        
        for mark in scale_marks:
            mark_y = mark['y_pos']
            
            # 在图像坐标系中，Y值越小越靠上
            if mark_y < water_y:  # 刻度在水位线上方
                if above_mark is None or mark['value'] > above_mark['value']:
                    above_mark = mark
            elif mark_y > water_y:  # 刻度在水位线下方
                if below_mark is None or mark['value'] < below_mark['value']:
                    below_mark = mark
        
        # 如果找到了上下两个刻度，进行插值计算
        if above_mark and below_mark:
            # 计算插值
            y_diff = below_mark['y_pos'] - above_mark['y_pos']
            water_offset = water_y - above_mark['y_pos']
            
            if y_diff > 0:
                ratio = water_offset / y_diff
                value_diff = below_mark['value'] - above_mark['value']
                precise_level = above_mark['value'] + ratio * value_diff
                
                return {
                    'precise_value': round(precise_level, 2),
                    'unit': 'M',
                    'confidence': 0.9,
                    'method': 'interpolation',
                    'reference_marks': [above_mark, below_mark],
                    'water_line_info': water_line
                }
        
        # 如果只有一个参考刻度，使用像素距离估算
        elif above_mark or below_mark:
            ref_mark = above_mark or below_mark
            pixel_diff = water_y - ref_mark['y_pos']
            value_offset = pixel_diff / pixel_per_unit
            
            # 在图像坐标系中，Y值增加意味着水位降低
            precise_level = ref_mark['value'] - value_offset
            
            return {
                'precise_value': round(precise_level, 2),
                'unit': 'M',
                'confidence': 0.7,
                'method': 'single_reference',
                'reference_marks': [ref_mark],
                'water_line_info': water_line
            }
        
        return None
    
    def process_image(self, image_path):
        """
        完整的图像处理流程
        """
        print(f"开始处理图像: {image_path}")
        
        # 1. 预处理图像
        original, ruler_region, mask = self.preprocess_ruler_image(image_path)
        
        # 2. 提取ROI
        roi = self.extract_ruler_roi(ruler_region, mask)
        
        # 3. 增强文字区域
        enhanced = self.enhance_text_regions(roi)
        
        # 4. OCR识别
        ocr_results = []
        
        if self.ocr_available:
            if hasattr(self.ocr, 'ocr'):  # PaddleOCR
                print("使用PaddleOCR进行识别...")
                # 传入增强后的图像进行识别
                ocr_results = self.recognize_with_paddleocr(roi, enhanced_image=enhanced)
            elif hasattr(self.ocr, 'image_to_string'):  # Tesseract
                print("使用Tesseract进行识别...")
                ocr_results = self.recognize_with_tesseract(enhanced)
        
        # 5. 提取水位数值
        water_levels = self.extract_water_level_values(ocr_results)
        
        # 6. 检测水位线
        print("检测水位线...")
        water_line = self.detect_water_line(roi)
        
        # 7. 计算刻度间距
        print("计算刻度间距...")
        scale_marks, pixel_per_unit = self.calculate_scale_spacing(ocr_results, roi.shape[0])
        
        # 8. 计算精确水位
        precise_water_level = None
        if water_line and scale_marks and pixel_per_unit:
            print("计算精确水位...")
            precise_water_level = self.calculate_precise_water_level(water_line, scale_marks, pixel_per_unit)
        
        return {
            'original_image': original,
            'processed_roi': roi,
            'enhanced_image': enhanced,
            'ocr_results': ocr_results,
            'water_levels': water_levels,
            'water_line': water_line,
            'scale_marks': scale_marks,
            'pixel_per_unit': pixel_per_unit,
            'precise_water_level': precise_water_level
        }
    
    def visualize_results(self, results):
        """
        可视化处理结果
        """
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        
        # 原始图像
        axes[0, 0].imshow(cv2.cvtColor(results['original_image'], cv2.COLOR_BGR2RGB))
        axes[0, 0].set_title('原始图像')
        axes[0, 0].axis('off')
        
        # 提取的ROI
        axes[0, 1].imshow(cv2.cvtColor(results['processed_roi'], cv2.COLOR_BGR2RGB))
        axes[0, 1].set_title('提取的标尺区域')
        axes[0, 1].axis('off')
        
        # 增强后的图像
        axes[0, 2].imshow(results['enhanced_image'], cmap='gray')
        axes[0, 2].set_title('增强后的图像')
        axes[0, 2].axis('off')
        
        # OCR识别结果
        result_img = results['processed_roi'].copy()
        for ocr_result in results['ocr_results']:
            if ocr_result.get('bbox'):
                bbox = ocr_result['bbox']
                pts = np.array(bbox, np.int32)
                cv2.polylines(result_img, [pts], True, (0, 255, 0), 2)
                
                # 添加文字标注
                center = np.mean(pts, axis=0).astype(int)
                cv2.putText(result_img, ocr_result['text'],
                           tuple(center), cv2.FONT_HERSHEY_SIMPLEX,
                           0.8, (255, 0, 0), 2)
        
        axes[1, 0].imshow(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
        axes[1, 0].set_title('OCR识别结果')
        axes[1, 0].axis('off')
        
        # 水位线检测结果
        water_line_img = results['processed_roi'].copy()
        
        # 绘制检测到的水位线
        if results.get('water_line'):
            water_line = results['water_line']
            line = water_line['line']
            x1, y1, x2, y2 = line
            cv2.line(water_line_img, (x1, y1), (x2, y2), (0, 0, 255), 3)
            
            # 标注水位线
            cv2.putText(water_line_img, f"Water Line",
                       (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX,
                       0.7, (0, 0, 255), 2)
        
        # 绘制刻度标记
        if results.get('scale_marks'):
            for mark in results['scale_marks']:
                y_pos = int(mark['y_pos'])
                cv2.circle(water_line_img, (50, y_pos), 5, (255, 0, 0), -1)
                cv2.putText(water_line_img, f"{mark['value']:.0f}M",
                           (60, y_pos+5), cv2.FONT_HERSHEY_SIMPLEX,
                           0.5, (255, 0, 0), 1)
        
        axes[1, 1].imshow(cv2.cvtColor(water_line_img, cv2.COLOR_BGR2RGB))
        axes[1, 1].set_title('水位线检测结果')
        axes[1, 1].axis('off')
        
        # 精确水位计算结果显示
        axes[1, 2].axis('off')
        result_text = "精确水位计算结果:\n\n"
        
        if results.get('precise_water_level'):
            precise = results['precise_water_level']
            result_text += f"精确水位: {precise['precise_value']}{precise['unit']}\n"
            result_text += f"置信度: {precise['confidence']:.2f}\n"
            result_text += f"计算方法: {precise['method']}\n\n"
            
            if precise.get('reference_marks'):
                result_text += "参考刻度:\n"
                for mark in precise['reference_marks']:
                    result_text += f"  - {mark['value']:.0f}M\n"
        else:
            result_text += "未能计算出精确水位\n"
            result_text += "可能原因:\n"
            result_text += "- 未检测到水位线\n"
            result_text += "- 刻度标记不足\n"
            result_text += "- 像素比例计算失败\n"
        
        if results.get('pixel_per_unit'):
            result_text += f"\n像素/米比例: {results['pixel_per_unit']:.2f}"
        
        axes[1, 2].text(0.1, 0.9, result_text, transform=axes[1, 2].transAxes,
                       fontsize=12, verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        axes[1, 2].set_title('计算结果')
        
        plt.tight_layout()
        plt.show()

def main():
    """主函数 - 演示如何使用"""
    # 创建OCR处理器
    processor = WaterLevelRulerOCR()
    
    # 图像路径（请替换为您的实际图像路径）
    image_path = "water.png"
    
    # 检查图像是否存在
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        print("请将您的水位标尺图像保存为 'water.png'")
        print("或者修改代码中的 image_path 变量")
        return
    
    try:
        # 处理图像
        results = processor.process_image(image_path)
        
        # 显示识别结果
        print("\n" + "="*50)
        print("🔍 OCR识别结果:")
        print("="*50)
        
        for i, result in enumerate(results['ocr_results'], 1):
            print(f"{i}. 识别文字: '{result['text']}'")
            print(f"   置信度: {result['confidence']:.2f}")
            print()
        
        # 显示提取的水位数值
        print("="*50)
        print("📏 提取的水位数值:")
        print("="*50)
        
        if results['water_levels']:
            for i, level in enumerate(results['water_levels'], 1):
                print(f"{i}. 水位读数: {level['value']}{level['unit']}")
                print(f"   置信度: {level['confidence']:.2f}")
                print(f"   原始文字: '{level['original_text']}'")
                print()
            
            # 找到最可能的水位读数
            best_reading = max(results['water_levels'], key=lambda x: x['confidence'])
            print("🎯 最可能的水位读数:")
            print(f"   {best_reading['value']}{best_reading['unit']} (置信度: {best_reading['confidence']:.2f})")
        else:
            print("❌ 未识别到有效的水位数值")
        
        # 显示精确水位计算结果
        print("\n" + "="*50)
        print("🎯 精确水位计算结果:")
        print("="*50)
        
        if results.get('precise_water_level'):
            precise = results['precise_water_level']
            print(f"✅ 精确水位: {precise['precise_value']}{precise['unit']}")
            print(f"   计算置信度: {precise['confidence']:.2f}")
            print(f"   计算方法: {precise['method']}")
            
            if precise.get('reference_marks'):
                print(f"   参考刻度: ", end="")
                marks = [f"{mark['value']:.0f}M" for mark in precise['reference_marks']]
                print(", ".join(marks))
            
            if results.get('pixel_per_unit'):
                print(f"   像素比例: {results['pixel_per_unit']:.2f} pixels/meter")
                
        else:
            print("❌ 未能计算出精确水位")
            print("原因分析:")
            if not results.get('water_line'):
                print("  - 未检测到水位线")
            if not results.get('scale_marks'):
                print("  - 未识别到足够的刻度标记")
            if not results.get('pixel_per_unit'):
                print("  - 无法计算像素-米转换比例")
            
            print("\n改进建议:")
            print("1. 确保图像中水位线清晰可见")
            print("2. 检查标尺刻度标记是否清晰")
            print("3. 尝试调整图像对比度和亮度")
            print("4. 确保标尺垂直且无倾斜")
        
        # 显示检测统计信息
        print("\n" + "="*50)
        print("📊 检测统计信息:")
        print("="*50)
        
        if results.get('water_line'):
            water_line = results['water_line']
            print(f"✅ 水位线检测: 成功")
            print(f"   线段长度: {water_line['length']:.1f} pixels")
            print(f"   倾斜角度: {water_line['angle']:.1f}°")
        else:
            print("❌ 水位线检测: 失败")
        
        if results.get('scale_marks'):
            print(f"✅ 刻度识别: {len(results['scale_marks'])} 个")
            for mark in results['scale_marks']:
                print(f"   - {mark['value']:.0f}M (Y坐标: {mark['y_pos']:.0f})")
        else:
            print("❌ 刻度识别: 无有效刻度")
        
        # 可视化结果
        print("\n📊 正在显示可视化结果...")
        processor.visualize_results(results)
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

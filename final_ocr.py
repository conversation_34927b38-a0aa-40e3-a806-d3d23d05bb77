#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
from paddleocr import PaddleOCR

def analyze_water_level_image():
    """
    使用PaddleOCR识别水位测量图像中的数字和文本
    """
    print("=== 水位测量图像OCR识别程序 ===")
    
    # 检查图像文件
    image_path = "water.png"
    if not os.path.exists(image_path):
        print(f"错误：图像文件 {image_path} 不存在")
        return
    
    print(f"找到图像文件: {image_path}")
    print("正在初始化PaddleOCR（中英文模型）...")
    
    try:
        # 初始化PaddleOCR
        ocr = PaddleOCR(lang='ch')  # 使用中文模型
        
        print("正在进行OCR文字识别...")
        
        # 执行OCR识别
        results = ocr.ocr(image_path)
        
        print("\n" + "="*60)
        print("OCR识别结果")
        print("="*60)
        
        if not results or not results[0]:
            print("未识别到任何文字内容")
            return
        
        # 存储识别结果
        all_texts = []
        all_numbers = []
        
        # 处理识别结果
        for idx, line in enumerate(results[0]):
            try:
                # PaddleOCR返回格式: [bbox, (text, confidence)]
                bbox = line[0]  # 边界框坐标
                text_data = line[1]  # 文本和置信度
                
                if isinstance(text_data, (list, tuple)) and len(text_data) >= 2:
                    text = text_data[0]
                    confidence = text_data[1]
                else:
                    text = str(text_data)
                    confidence = 0.0
                
                print(f"\n第 {idx+1} 个识别结果:")
                print(f"  文本内容: '{text}'")
                print(f"  识别置信度: {confidence:.4f}")
                print(f"  文本位置: {bbox}")
                
                all_texts.append(text)
                
                # 提取数字
                numbers_found = re.findall(r'\d+\.?\d*', text)
                if numbers_found:
                    all_numbers.extend(numbers_found)
                    print(f"  包含数字: {numbers_found}")
                
                print("-" * 40)
                
            except Exception as e:
                print(f"处理第 {idx+1} 个结果时出错: {e}")
                continue
        
        # 汇总分析
        print(f"\n{'='*60}")
        print("识别结果汇总分析")
        print("="*60)
        
        print(f"识别到的所有文本: {all_texts}")
        print(f"提取到的所有数字: {all_numbers}")
        
        # 合并所有文本进行整体分析
        full_text = ' '.join(all_texts)
        print(f"完整文本内容: '{full_text}'")
        
        # 分析特定模式
        print(f"\n{'='*60}")
        print("特定信息提取")
        print("="*60)
        
        # 时间戳模式
        timestamp_patterns = [
            (r'\d{4}年\d{1,2}月\d{1,2}日\s*\d{1,2}:\d{2}:\d{2}', '完整时间戳'),
            (r'\d{4}年\d{1,2}月\d{1,2}日', '日期'),
            (r'\d{1,2}:\d{2}:\d{2}', '时间'),
        ]
        
        for pattern, desc in timestamp_patterns:
            matches = re.findall(pattern, full_text)
            if matches:
                print(f"{desc}: {matches}")
        
        # 水位读数模式
        water_level_patterns = [
            (r'\d+\.?\d*\s*(?:cm|CM|厘米|毫米|mm|MM)', '带单位的测量值'),
            (r'\d+\.?\d*', '纯数字'),
        ]
        
        for pattern, desc in water_level_patterns:
            matches = re.findall(pattern, full_text)
            if matches:
                print(f"{desc}: {matches}")
        
        print(f"\n{'='*60}")
        print("识别任务完成")
        print("="*60)
        
        return {
            'texts': all_texts,
            'numbers': all_numbers,
            'full_text': full_text
        }
        
    except Exception as e:
        print(f"OCR处理过程中发生错误: {e}")
        return None

if __name__ == "__main__":
    analyze_water_level_image()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import cv2
import numpy as np
from paddleocr import PaddleOCR
from PIL import Image, ImageEnhance, ImageFilter
import os
import re

def preprocess_image(image_path, output_path="preprocessed_image.jpg"):
    """
    预处理图像以提高OCR识别率
    """
    print("正在预处理图像...")
    
    # 使用OpenCV读取图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图像: {image_path}")
        return None
    
    # 转换为灰度图
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # 应用多种图像增强技术
    print("1. 应用图像增强...")
    
    # 1. 对比度增强
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)
    
    # 2. 去噪
    denoised = cv2.fastNlMeansDenoising(enhanced, None, 10, 7, 21)
    
    # 3. 锐化
    kernel = np.array([[-1,-1,-1],
                       [-1, 9,-1],
                       [-1,-1,-1]])
    sharpened = cv2.filter2D(denoised, -1, kernel)
    
    # 4. 二值化处理
    _, binary = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    # 5. 形态学操作
    kernel = np.ones((2,2), np.uint8)
    morph = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    
    # 保存预处理后的图像
    cv2.imwrite(output_path, morph)
    print(f"预处理后的图像已保存为: {output_path}")
    
    # 使用PIL进行额外的增强
    pil_img = Image.open(output_path)
    
    # 增强对比度
    enhancer = ImageEnhance.Contrast(pil_img)
    pil_img = enhancer.enhance(2.0)
    
    # 增强锐度
    enhancer = ImageEnhance.Sharpness(pil_img)
    pil_img = enhancer.enhance(2.0)
    
    # 保存最终增强后的图像
    enhanced_path = "enhanced_" + output_path
    pil_img.save(enhanced_path)
    print(f"增强后的图像已保存为: {enhanced_path}")
    
    return enhanced_path

def analyze_ruler_image(image_path):
    """
    分析水位标尺图像
    """
    print("\n=== 水位标尺OCR识别程序 ===")
    
    # 检查图像文件
    if not os.path.exists(image_path):
        print(f"错误：图像文件 {image_path} 不存在")
        return
    
    print(f"找到图像文件: {image_path}")
    
    # 预处理图像
    preprocessed_path = preprocess_image(image_path)
    if not preprocessed_path:
        return
    
    print("\n正在初始化PaddleOCR...")
    
    try:
        # 初始化PaddleOCR，使用更高精度的参数
        ocr = PaddleOCR(
            lang='ch',
            det_db_thresh=0.3,  # 降低检测阈值
            det_db_box_thresh=0.5,  # 降低框阈值
            rec_batch_num=1,  # 单批处理
            max_text_length=25,  # 增加最大文本长度
            use_angle_cls=True,  # 启用角度分类
            cls_thresh=0.9  # 角度分类阈值
        )
        
        print("正在进行OCR识别...")
        
        # 对原始图像和预处理后的图像都进行识别
        results_original = ocr.ocr(image_path)
        results_preprocessed = ocr.ocr(preprocessed_path)
        
        print("\n" + "="*60)
        print("原始图像OCR识别结果")
        print("="*60)
        process_ocr_results(results_original, "原始图像")
        
        print("\n" + "="*60)
        print("预处理图像OCR识别结果")
        print("="*60)
        process_ocr_results(results_preprocessed, "预处理图像")
        
        # 尝试使用不同的预处理参数
        print("\n尝试其他预处理方法...")
        
        # 使用自适应阈值
        img = cv2.imread(image_path, 0)
        adaptive_thresh = cv2.adaptiveThreshold(img, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                              cv2.THRESH_BINARY, 11, 2)
        adaptive_path = "adaptive_threshold.jpg"
        cv2.imwrite(adaptive_path, adaptive_thresh)
        
        results_adaptive = ocr.ocr(adaptive_path)
        print("\n" + "="*60)
        print("自适应阈值图像OCR识别结果")
        print("="*60)
        process_ocr_results(results_adaptive, "自适应阈值图像")
        
    except Exception as e:
        print(f"OCR处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def process_ocr_results(results, image_type):
    """
    处理OCR识别结果
    """
    if not results or not results[0]:
        print(f"{image_type}未识别到任何文字内容")
        return
    
    all_texts = []
    all_numbers = []
    
    for idx, line in enumerate(results[0]):
        try:
            if isinstance(line, list) and len(line) >= 2:
                bbox = line[0]
                text_info = line[1]
                
                if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                    text = text_info[0]
                    confidence = text_info[1]
                else:
                    text = str(text_info)
                    confidence = 0.0
                
                print(f"\n识别结果 {idx+1}:")
                print(f"  文本内容: '{text}'")
                print(f"  识别置信度: {confidence:.4f}")
                
                if text and len(text.strip()) > 0:
                    all_texts.append(text)
                    
                    # 提取数字
                    numbers_found = re.findall(r'\d+\.?\d*', text)
                    if numbers_found:
                        all_numbers.extend(numbers_found)
                        print(f"  包含数字: {numbers_found}")
                
        except Exception as e:
            print(f"处理第 {idx+1} 个结果时出错: {e}")
            continue
    
    print(f"\n汇总:")
    print(f"识别到的所有文本: {all_texts}")
    print(f"提取到的所有数字: {all_numbers}")
    
    # 尝试识别标尺刻度
    ruler_numbers = []
    for num in all_numbers:
        try:
            value = float(num)
            # 标尺刻度通常是整数或简单的小数
            if 0 <= value <= 1000:  # 假设标尺范围在0-1000之间
                ruler_numbers.append(value)
        except:
            pass
    
    if ruler_numbers:
        print(f"可能的标尺刻度值: {sorted(ruler_numbers)}")

def main():
    """
    主函数
    """
    # 检查可能的图像文件
    possible_images = ["temp_ocr_image.jpg", "water.png", "water_level_image.jpg"]
    
    image_path = None
    for img in possible_images:
        if os.path.exists(img):
            image_path = img
            break
    
    if image_path:
        analyze_ruler_image(image_path)
    else:
        print("未找到任何图像文件")
        print(f"请确保以下文件之一存在: {possible_images}")

if __name__ == "__main__":
    main()
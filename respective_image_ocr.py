import cv2
import numpy as np
import matplotlib.pyplot as plt
from paddleocr import PaddleOCR

class WaterGaugePerspectiveCorrector:
    """水位表透视变换矫正器"""
    
    def __init__(self):
        self.src_points = None
        self.dst_points = None
        self.transform_matrix = None
        
    def manual_select_points(self, image_path):
        """
        手动选择四个角点进行透视变换
        选择顺序：左上、右上、右下、左下
        """
        img = cv2.imread(image_path)
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        print("请按顺序点击水位标尺的四个角点：左上、右上、右下、左下")
        print("点击完成后关闭窗口")
        
        points = []
        
        def mouse_callback(event, x, y, flags, param):
            if event == cv2.EVENT_LBUTTONDOWN:
                points.append([x, y])
                # 在图像上绘制点
                cv2.circle(img_rgb, (x, y), 5, (255, 0, 0), -1)
                if len(points) > 1:
                    # 绘制连线
                    cv2.line(img_rgb, tuple(points[-2]), tuple(points[-1]), (0, 255, 0), 2)
                cv2.imshow('Select Points', cv2.cvtColor(img_rgb, cv2.COLOR_RGB2BGR))
                
                if len(points) == 4:
                    # 连接最后一个点和第一个点
                    cv2.line(img_rgb, tuple(points[-1]), tuple(points[0]), (0, 255, 0), 2)
                    cv2.imshow('Select Points', cv2.cvtColor(img_rgb, cv2.COLOR_RGB2BGR))
        
        cv2.namedWindow('Select Points')
        cv2.setMouseCallback('Select Points', mouse_callback)
        cv2.imshow('Select Points', cv2.cvtColor(img_rgb, cv2.COLOR_RGB2BGR))
        cv2.waitKey(0)
        cv2.destroyAllWindows()
        
        if len(points) == 4:
            self.src_points = np.float32(points)
            return True
        return False
    
    def auto_detect_rectangle(self, image_path, debug=False):
        """
        自动检测矩形区域（水位标尺）
        """
        img = cv2.imread(image_path)
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 预处理
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 边缘检测
        edges = cv2.Canny(blurred, 50, 150)
        
        # 形态学操作，连接断开的边缘
        kernel = np.ones((3, 3), np.uint8)
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 找到最大的矩形轮廓
        max_area = 0
        best_contour = None
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > max_area:
                # 近似多边形
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # 检查是否为四边形
                if len(approx) == 4:
                    max_area = area
                    best_contour = approx
        
        if best_contour is not None:
            # 重新排序角点：左上、右上、右下、左下
            points = best_contour.reshape(4, 2)
            self.src_points = self.order_points(points)
            
            if debug:
                # 显示检测结果
                debug_img = img.copy()
                cv2.drawContours(debug_img, [best_contour], -1, (0, 255, 0), 2)
                for point in self.src_points:
                    cv2.circle(debug_img, tuple(point.astype(int)), 5, (255, 0, 0), -1)
                cv2.imshow('Detected Rectangle', debug_img)
                cv2.waitKey(0)
                cv2.destroyAllWindows()
            
            return True
        return False
    
    def order_points(self, pts):
        """
        对四个点进行排序：左上、右上、右下、左下
        """
        rect = np.zeros((4, 2), dtype="float32")
        
        # 计算左上和右下
        s = pts.sum(axis=1)
        rect[0] = pts[np.argmin(s)]  # 左上
        rect[2] = pts[np.argmax(s)]  # 右下
        
        # 计算右上和左下
        diff = np.diff(pts, axis=1)
        rect[1] = pts[np.argmin(diff)]  # 右上
        rect[3] = pts[np.argmax(diff)]  # 左下
        
        return rect
    
    def calculate_transform(self, target_width=300, target_height=800):
        """
        计算透视变换矩阵
        target_width: 目标图像宽度
        target_height: 目标图像高度
        """
        if self.src_points is None:
            raise ValueError("源点未设置")
        
        # 定义目标点（矩形）
        self.dst_points = np.float32([
            [0, 0],                          # 左上
            [target_width - 1, 0],           # 右上
            [target_width - 1, target_height - 1],  # 右下
            [0, target_height - 1]           # 左下
        ])
        
        # 计算透视变换矩阵
        self.transform_matrix = cv2.getPerspectiveTransform(self.src_points, self.dst_points)
        
        return self.transform_matrix
    
    def apply_transform(self, image_path, output_path=None):
        """
        应用透视变换
        """
        if self.transform_matrix is None:
            raise ValueError("变换矩阵未计算")
        
        img = cv2.imread(image_path)
        
        # 获取目标尺寸
        target_width = int(self.dst_points[1][0] + 1)
        target_height = int(self.dst_points[2][1] + 1)
        
        # 应用透视变换
        warped = cv2.warpPerspective(img, self.transform_matrix, (target_width, target_height))
        
        if output_path:
            cv2.imwrite(output_path, warped)
        
        return warped
    
    def process_for_ocr(self, warped_image):
        """
        对矫正后的图像进行预处理，优化OCR识别
        """
        # 转换为灰度图
        gray = cv2.cvtColor(warped_image, cv2.COLOR_BGR2GRAY)
        
        # 增强对比度
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        # 二值化
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 去噪
        denoised = cv2.medianBlur(binary, 3)
        
        return denoised
    
    def extract_roi_for_numbers(self, warped_image):
        """
        提取数字区域的ROI（感兴趣区域）
        """
        height, width = warped_image.shape[:2]
        
        # 假设数字主要在标尺的中间区域
        # 可以根据实际情况调整
        roi_x = int(width * 0.2)  # 左边界
        roi_width = int(width * 0.6)  # 宽度
        
        roi = warped_image[:, roi_x:roi_x + roi_width]
        
        return roi

def demo_perspective_correction():
    """
    演示完整的透视矫正和OCR识别流程
    """
    # 初始化矫正器
    corrector = WaterGaugePerspectiveCorrector()
    
    # 图像路径
    image_path = 'water.png'
    
    # 方法1：自动检测矩形区域
    print("尝试自动检测水位标尺区域...")
    if corrector.auto_detect_rectangle(image_path, debug=True):
        print("自动检测成功！")
    else:
        print("自动检测失败，请手动选择四个角点")
        corrector.manual_select_points(image_path)
    
    # 计算变换矩阵
    corrector.calculate_transform(target_width=300, target_height=800)
    
    # 应用透视变换
    warped = corrector.apply_transform(image_path, 'warped_gauge.jpg')
    print("透视变换完成，保存为 warped_gauge.jpg")
    
    # 预处理优化OCR
    processed = corrector.process_for_ocr(warped)
    cv2.imwrite('processed_gauge.jpg', processed)
    print("预处理完成，保存为 processed_gauge.jpg")
    
    # 提取ROI
    roi = corrector.extract_roi_for_numbers(warped)
    cv2.imwrite('roi_gauge.jpg', roi)
    print("ROI提取完成，保存为 roi_gauge.jpg")
    
    # 使用PaddleOCR识别
    print("\n开始OCR识别...")
    ocr = PaddleOCR(use_textline_orientation=True, lang='en')
    
    # 识别原图
    result_original = ocr.predict(image_path)
    print("\n原图识别结果：")
    if result_original and result_original[0]:
        for line in result_original[0]:
            if len(line) >= 2 and len(line[1]) >= 2:
                print(f"文本: {line[1][0]}, 置信度: {line[1][1]:.3f}")
    else:
        print("未检测到文本")

    # 识别矫正后的图像
    result_warped = ocr.predict('warped_gauge.jpg')
    print("\n矫正后识别结果：")
    if result_warped and result_warped[0]:
        for line in result_warped[0]:
            if len(line) >= 2 and len(line[1]) >= 2:
                print(f"文本: {line[1][0]}, 置信度: {line[1][1]:.3f}")
    else:
        print("未检测到文本")

    # 识别预处理后的图像
    result_processed = ocr.predict('processed_gauge.jpg')
    print("\n预处理后识别结果：")
    if result_processed and result_processed[0]:
        for line in result_processed[0]:
            if len(line) >= 2 and len(line[1]) >= 2:
                print(f"文本: {line[1][0]}, 置信度: {line[1][1]:.3f}")
    else:
        print("未检测到文本")
    
    # 显示对比结果
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    original = cv2.imread(image_path)
    original_rgb = cv2.cvtColor(original, cv2.COLOR_BGR2RGB)
    axes[0].imshow(original_rgb)
    axes[0].set_title('原始图像')
    axes[0].axis('off')
    
    warped_rgb = cv2.cvtColor(warped, cv2.COLOR_BGR2RGB)
    axes[1].imshow(warped_rgb)
    axes[1].set_title('透视矫正后')
    axes[1].axis('off')
    
    axes[2].imshow(processed, cmap='gray')
    axes[2].set_title('预处理后')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    # 运行演示
    demo_perspective_correction()
import cv2
import numpy as np
from paddleocr import PaddleOCR
from PIL import Image
import os

def process_water_level_image(image_path):
    """
    使用PaddleOCR识别水位测量图像中的数字和文本
    """
    # 初始化PaddleOCR，使用中英文模型
    ocr = PaddleOCR(use_textline_orientation=True, lang='ch')
    
    # 读取图像
    if not os.path.exists(image_path):
        print(f"错误：图像文件 {image_path} 不存在")
        return None
    
    print(f"正在处理图像: {image_path}")
    
    # 使用PaddleOCR进行文字识别
    result = ocr.predict(image_path)
    
    print("=== PaddleOCR 识别结果 ===")
    print(f"处理图像: {image_path}")
    print("-" * 50)
    
    if result and result[0]:
        # 存储所有识别到的文本和数字
        all_texts = []
        numbers = []
        
        for idx, line in enumerate(result[0]):
            # line格式: [[[x1,y1],[x2,y2],[x3,y3],[x4,y4]], (text, confidence)]
            bbox = line[0]  # 边界框坐标
            text_info = line[1]  # (文本, 置信度)
            text = text_info[0]
            confidence = text_info[1]
            
            print(f"第 {idx+1} 个识别结果:")
            print(f"  文本: {text}")
            print(f"  置信度: {confidence:.4f}")
            print(f"  位置: {bbox}")
            
            all_texts.append(text)
            
            # 提取数字
            import re
            # 匹配数字（包括小数）
            number_matches = re.findall(r'\d+\.?\d*', text)
            if number_matches:
                numbers.extend(number_matches)
                print(f"  提取的数字: {number_matches}")
            
            print("-" * 30)
        
        print("\n=== 汇总结果 ===")
        print(f"识别到的所有文本: {all_texts}")
        print(f"提取的所有数字: {numbers}")
        
        # 特别关注时间戳和水位读数
        timestamp_pattern = r'\d{4}年\d{2}月\d{2}日\s*\d{2}:\d{2}:\d{2}'
        water_level_pattern = r'\d+\.?\d*[cm|CM|厘米]*'
        
        timestamps = []
        water_levels = []
        
        for text in all_texts:
            # 查找时间戳
            time_matches = re.findall(timestamp_pattern, text)
            if time_matches:
                timestamps.extend(time_matches)
            
            # 查找可能的水位读数
            level_matches = re.findall(water_level_pattern, text)
            if level_matches:
                water_levels.extend(level_matches)
        
        if timestamps:
            print(f"识别到的时间戳: {timestamps}")
        if water_levels:
            print(f"可能的水位读数: {water_levels}")
        
        return {
            'all_texts': all_texts,
            'numbers': numbers,
            'timestamps': timestamps,
            'water_levels': water_levels,
            'raw_result': result
        }
    else:
        print("未识别到任何文字")
        return None

def save_image_from_clipboard_or_url(save_path="water_level_image.jpg"):
    """
    保存图像文件（这里我们假设用户会手动保存图像）
    """
    print(f"请将要识别的图像保存为: {save_path}")
    return save_path

def main():
    print("=== 水位测量图像OCR识别程序 ===")
    
    # 尝试多个可能的图像文件路径
    possible_paths = ["water.png", "water_level_image.jpg", "water_level_image.png"]
    image_path = None
    
    for path in possible_paths:
        if os.path.exists(path):
            image_path = path
            print(f"找到图像文件: {path}")
            break
    
    if not image_path:
        print("未找到图像文件，请确保以下文件之一存在:")
        for path in possible_paths:
            print(f"  - {path}")
        return
    
    # 处理图像
    result = process_water_level_image(image_path)
    
    if result:
        print("\n=== 识别完成 ===")
        print("程序已成功识别图像中的文字和数字")
    else:
        print("\n=== 识别失败 ===")
        print("未能识别到图像中的文字")

if __name__ == "__main__":
    main()
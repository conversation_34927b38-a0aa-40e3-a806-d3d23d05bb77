#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用水位标尺OCR识别系统
自动识别各种水位标尺图像中的水位值
支持批量处理和多种标尺类型
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import re
import os
from typing import List, Dict, Tuple, Optional

class UniversalWaterLevelOCR:
    """通用水位标尺OCR识别类"""
    
    def __init__(self):
        """初始化"""
        self.ocr = None
        self.ocr_available = False
        self.debug_mode = True  # 调试模式，显示处理步骤
        self._init_ocr()
    
    def _init_ocr(self):
        """初始化OCR引擎"""
        # 尝试PaddleOCR
        try:
            from paddleocr import PaddleOCR
            self.ocr = PaddleOCR(
                use_angle_cls=True,
                lang='ch',
                show_log=False
            )
            self.ocr_available = True
            self.ocr_type = 'paddle'
            print("✓ PaddleOCR初始化成功")
            return
        except Exception as e:
            print(f"× PaddleOCR初始化失败: {e}")
        
        # 尝试EasyOCR
        try:
            import easyocr
            self.ocr = easyocr.Reader(['ch_sim', 'en'])
            self.ocr_available = True
            self.ocr_type = 'easy'
            print("✓ EasyOCR初始化成功")
            return
        except Exception as e:
            print(f"× EasyOCR初始化失败: {e}")
        
        # 尝试Tesseract
        try:
            import pytesseract
            self.ocr = pytesseract
            self.ocr_available = True
            self.ocr_type = 'tesseract'
            print("✓ Tesseract OCR初始化成功")
            return
        except Exception as e:
            print(f"× Tesseract初始化失败: {e}")
        
        print("⚠ 未找到可用的OCR引擎")
    
    def detect_and_correct_skew(self, image: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        检测并矫正图像倾斜
        
        返回:
            矫正后的图像和旋转角度
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 方法1: 基于边缘的倾斜检测
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)
        
        # 霍夫线检测
        lines = cv2.HoughLines(edges, 1, np.pi/180, 100)
        
        if lines is not None:
            # 统计主要角度
            angles = []
            for rho, theta in lines[:, 0]:
                # 转换为度数
                angle = (theta * 180 / np.pi) - 90
                # 标准化角度到 -45 到 45 度范围
                if angle < -45:
                    angle += 90
                elif angle > 45:
                    angle -= 90
                angles.append(angle)
            
            if angles:
                # 使用中位数作为倾斜角度（对异常值更鲁棒）
                median_angle = np.median(angles)
                
                # 如果倾斜角度显著（大于2度），进行矫正
                if abs(median_angle) > 2:
                    print(f"   检测到倾斜角度: {median_angle:.1f}°")
                    
                    # 计算旋转矩阵
                    (h, w) = image.shape[:2]
                    center = (w // 2, h // 2)
                    M = cv2.getRotationMatrix2D(center, median_angle, 1.0)
                    
                    # 计算新的边界尺寸
                    cos = np.abs(M[0, 0])
                    sin = np.abs(M[0, 1])
                    new_w = int((h * sin) + (w * cos))
                    new_h = int((h * cos) + (w * sin))
                    
                    # 调整旋转矩阵
                    M[0, 2] += (new_w / 2) - center[0]
                    M[1, 2] += (new_h / 2) - center[1]
                    
                    # 执行旋转
                    rotated = cv2.warpAffine(image, M, (new_w, new_h),
                                           flags=cv2.INTER_CUBIC,
                                           borderMode=cv2.BORDER_REPLICATE)
                    
                    return rotated, median_angle
        
        # 方法2: 基于文本方向的倾斜检测（如果方法1失败）
        # 使用形态学操作找到文本区域
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (30, 5))
        dilated = cv2.dilate(edges, kernel, iterations=1)
        
        # 找轮廓
        contours, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if contours:
            # 找到最大的轮廓
            largest_contour = max(contours, key=cv2.contourArea)
            
            # 计算最小旋转矩形
            rect = cv2.minAreaRect(largest_contour)
            angle = rect[2]
            
            # 标准化角度
            if angle < -45:
                angle += 90
            elif angle > 45:
                angle -= 90
            
            if abs(angle) > 2:
                print(f"   基于文本检测到倾斜角度: {angle:.1f}°")
                
                # 旋转图像
                (h, w) = image.shape[:2]
                center = (w // 2, h // 2)
                M = cv2.getRotationMatrix2D(center, angle, 1.0)
                
                cos = np.abs(M[0, 0])
                sin = np.abs(M[0, 1])
                new_w = int((h * sin) + (w * cos))
                new_h = int((h * cos) + (w * sin))
                
                M[0, 2] += (new_w / 2) - center[0]
                M[1, 2] += (new_h / 2) - center[1]
                
                rotated = cv2.warpAffine(image, M, (new_w, new_h),
                                       flags=cv2.INTER_CUBIC,
                                       borderMode=cv2.BORDER_REPLICATE)
                
                return rotated, angle
        
        return image, 0.0
    
    def auto_crop_after_rotation(self, image: np.ndarray) -> np.ndarray:
        """
        旋转后自动裁剪黑边
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 找到非黑色区域
        _, thresh = cv2.threshold(gray, 1, 255, cv2.THRESH_BINARY)
        
        # 找到内容的边界
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if contours:
            # 找到最大轮廓的边界框
            x, y, w, h = cv2.boundingRect(contours[0])
            for contour in contours[1:]:
                x2, y2, w2, h2 = cv2.boundingRect(contour)
                x = min(x, x2)
                y = min(y, y2)
                w = max(x + w, x2 + w2) - x
                h = max(y + h, y2 + h2) - y
            
            # 裁剪图像
            cropped = image[y:y+h, x:x+w]
            return cropped
        
        return image

    def preprocess_image(self, image: np.ndarray) -> Dict:
        """
        图像预处理，增强标尺区域
        """
        results = {}
        
        # 1. 检测并矫正倾斜
        print("   🔄 检测图像倾斜...")
        corrected_image, rotation_angle = self.detect_and_correct_skew(image)
        
        if abs(rotation_angle) > 0:
            print(f"   ✓ 已矫正 {rotation_angle:.1f}° 倾斜")
            # 裁剪黑边
            corrected_image = self.auto_crop_after_rotation(corrected_image)
            results['rotation_angle'] = rotation_angle
            results['corrected'] = corrected_image.copy()
        else:
            corrected_image = image
            results['rotation_angle'] = 0
        
        # 2. 尺寸标准化
        height, width = corrected_image.shape[:2]
        if width > 1920:
            scale = 1920 / width
            new_width = int(width * scale)
            new_height = int(height * scale)
            corrected_image = cv2.resize(corrected_image, (new_width, new_height))
        
        results['resized'] = corrected_image.copy()
        
        # 3. 色彩空间转换
        hsv = cv2.cvtColor(corrected_image, cv2.COLOR_BGR2HSV)
        lab = cv2.cvtColor(corrected_image, cv2.COLOR_BGR2LAB)
        gray = cv2.cvtColor(corrected_image, cv2.COLOR_BGR2GRAY)
        
        # 4. 检测白色标尺区域
        # HSV中的白色
        lower_white_hsv = np.array([0, 0, 180])
        upper_white_hsv = np.array([180, 30, 255])
        white_mask_hsv = cv2.inRange(hsv, lower_white_hsv, upper_white_hsv)
        
        # LAB中的白色（更准确）
        l_channel = lab[:, :, 0]
        white_mask_lab = (l_channel > 200).astype(np.uint8) * 255
        
        # 组合白色掩码
        white_mask = cv2.bitwise_or(white_mask_hsv, white_mask_lab)
        
        # 5. 检测红色刻度
        # 红色在HSV中的两个范围
        lower_red1 = np.array([0, 50, 50])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([170, 50, 50])
        upper_red2 = np.array([180, 255, 255])
        
        red_mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        red_mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        red_mask = cv2.bitwise_or(red_mask1, red_mask2)
        
        # 6. 组合掩码找到标尺
        ruler_mask = cv2.bitwise_or(white_mask, red_mask)
        
        # 7. 形态学操作清理
        kernel_close = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        kernel_open = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        
        ruler_mask = cv2.morphologyEx(ruler_mask, cv2.MORPH_CLOSE, kernel_close)
        ruler_mask = cv2.morphologyEx(ruler_mask, cv2.MORPH_OPEN, kernel_open)
        
        results['ruler_mask'] = ruler_mask
        results['white_mask'] = white_mask
        results['red_mask'] = red_mask
        results['gray'] = gray
        
        return results
    
    def detect_ruler_region(self, image: np.ndarray, mask: np.ndarray) -> Tuple[np.ndarray, Tuple[int, int, int, int]]:
        """
        精确定位标尺区域
        """
        # 查找轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return image, (0, 0, image.shape[1], image.shape[0])
        
        # 筛选可能的标尺轮廓
        ruler_candidates = []
        img_height, img_width = image.shape[:2]
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < 1000:  # 太小的区域忽略
                continue
            
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = h / w if w > 0 else 0
            
            # 标尺通常是垂直的长条形
            if aspect_ratio > 1.5:  # 高度大于宽度1.5倍
                ruler_candidates.append({
                    'contour': contour,
                    'bbox': (x, y, w, h),
                    'area': area,
                    'aspect_ratio': aspect_ratio,
                    'score': area * aspect_ratio  # 综合评分
                })
        
        if not ruler_candidates:
            # 如果没有找到垂直的，尝试找最大的白色区域
            largest_contour = max(contours, key=cv2.contourArea)
            x, y, w, h = cv2.boundingRect(largest_contour)
        else:
            # 选择评分最高的候选区域
            best_candidate = max(ruler_candidates, key=lambda x: x['score'])
            x, y, w, h = best_candidate['bbox']
        
        # 扩展边界以包含可能的文字
        margin_x = int(w * 0.2)
        margin_y = int(h * 0.05)
        
        x = max(0, x - margin_x)
        y = max(0, y - margin_y)
        w = min(img_width - x, w + 2 * margin_x)
        h = min(img_height - y, h + 2 * margin_y)
        
        ruler_roi = image[y:y+h, x:x+w]
        
        return ruler_roi, (x, y, w, h)
    
    def enhance_for_ocr(self, image: np.ndarray) -> List[np.ndarray]:
        """
        生成多个增强版本以提高OCR识别率
        """
        enhanced_versions = []
        
        # 版本1: 原始图像
        enhanced_versions.append(image)
        
        # 版本2: 灰度 + CLAHE增强
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced_gray = clahe.apply(gray)
        enhanced_versions.append(cv2.cvtColor(enhanced_gray, cv2.COLOR_GRAY2BGR))
        
        # 版本3: 二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        enhanced_versions.append(cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR))
        
        # 版本4: 自适应阈值
        adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                       cv2.THRESH_BINARY, 11, 2)
        enhanced_versions.append(cv2.cvtColor(adaptive, cv2.COLOR_GRAY2BGR))
        
        # 版本5: 锐化增强
        kernel_sharpen = np.array([[-1,-1,-1],
                                  [-1, 9,-1],
                                  [-1,-1,-1]])
        sharpened = cv2.filter2D(image, -1, kernel_sharpen)
        enhanced_versions.append(sharpened)
        
        # 版本6: 去噪 + 增强
        denoised = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
        enhanced_versions.append(denoised)
        
        return enhanced_versions
    
    def perform_ocr(self, image: np.ndarray) -> List[Dict]:
        """
        执行OCR识别
        """
        if not self.ocr_available:
            return []
        
        results = []
        
        try:
            if self.ocr_type == 'paddle':
                # PaddleOCR
                temp_path = "temp_ocr.jpg"
                cv2.imwrite(temp_path, image)
                ocr_results = self.ocr.ocr(temp_path, cls=True)
                
                if ocr_results and ocr_results[0]:
                    for line in ocr_results[0]:
                        if len(line) >= 2:
                            bbox = line[0]
                            text = line[1][0]
                            confidence = line[1][1]
                            
                            results.append({
                                'text': text,
                                'confidence': confidence,
                                'bbox': bbox,
                                'center_y': np.mean([p[1] for p in bbox])
                            })
                
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            
            elif self.ocr_type == 'easy':
                # EasyOCR
                ocr_results = self.ocr.readtext(image)
                for bbox, text, confidence in ocr_results:
                    results.append({
                        'text': text,
                        'confidence': confidence,
                        'bbox': bbox,
                        'center_y': np.mean([p[1] for p in bbox])
                    })
            
            elif self.ocr_type == 'tesseract':
                # Tesseract
                pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
                text = self.ocr.image_to_string(pil_image, lang='chi_sim+eng')
                if text.strip():
                    results.append({
                        'text': text.strip(),
                        'confidence': 0.5,
                        'bbox': None,
                        'center_y': image.shape[0] / 2
                    })
        
        except Exception as e:
            print(f"OCR错误: {e}")
        
        return results
    
    def extract_water_level_values(self, ocr_results: List[Dict]) -> List[Dict]:
        """
        从OCR结果中提取水位数值
        """
        water_levels = []
        
        # 定义各种可能的水位格式
        patterns = [
            (r'(\d{3,4})\s*[Mm]', 1.0),           # 376M, 377M
            (r'(\d{3,4})', 0.8),                   # 376, 377
            (r'(\d{2,3})\.(\d{1,2})\s*[Mm]', 0.9), # 76.5M, 376.5M
            (r'(\d{2,3})\.(\d{1,2})', 0.7),        # 76.5, 376.5
        ]
        
        for result in ocr_results:
            text = result['text'].strip()
            
            for pattern, pattern_confidence in patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    try:
                        if isinstance(match, tuple):
                            if len(match) == 2:
                                value = float(f"{match[0]}.{match[1]}")
                            else:
                                value = float(match[0])
                        else:
                            value = float(match)
                        
                        # 验证数值范围（水位通常在100-1000米）
                        if 50 <= value <= 1000:
                            water_levels.append({
                                'value': value,
                                'text': text,
                                'confidence': result['confidence'] * pattern_confidence,
                                'y_position': result.get('center_y', 0),
                                'bbox': result.get('bbox')
                            })
                            break
                    except ValueError:
                        continue
        
        # 按置信度排序
        water_levels.sort(key=lambda x: x['confidence'], reverse=True)
        
        return water_levels
    
    def detect_water_surface(self, image: np.ndarray) -> Optional[Dict]:
        """
        检测水面位置
        """
        height, width = image.shape[:2]
        
        # 方法1: 检测水平边缘（水面通常是强水平边缘）
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 使用Sobel算子检测水平边缘
        sobel_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        sobel_y = np.abs(sobel_y)
        sobel_y = (sobel_y / sobel_y.max() * 255).astype(np.uint8)
        
        # 找到最强的水平边缘
        horizontal_projection = np.sum(sobel_y, axis=1)
        
        # 平滑投影以减少噪声
        from scipy.ndimage import gaussian_filter1d
        smooth_projection = gaussian_filter1d(horizontal_projection, sigma=5)
        
        # 找到峰值
        peaks = []
        for i in range(10, len(smooth_projection) - 10):
            if smooth_projection[i] > np.mean(smooth_projection) * 1.5:
                # 局部最大值
                if smooth_projection[i] == max(smooth_projection[i-5:i+5]):
                    peaks.append((i, smooth_projection[i]))
        
        if peaks:
            # 选择最强的峰值作为水面
            best_peak = max(peaks, key=lambda x: x[1])
            return {
                'y': best_peak[0],
                'confidence': min(best_peak[1] / (np.max(smooth_projection) + 1e-6), 1.0),
                'method': 'edge_detection'
            }
        
        # 方法2: 颜色变化检测（水面上下颜色差异）
        # 计算颜色直方图的变化
        color_changes = []
        for y in range(10, height - 10):
            upper_region = image[y-10:y, :]
            lower_region = image[y:y+10, :]
            
            # 计算颜色差异
            upper_mean = np.mean(upper_region, axis=(0, 1))
            lower_mean = np.mean(lower_region, axis=(0, 1))
            color_diff = np.linalg.norm(upper_mean - lower_mean)
            
            color_changes.append(color_diff)
        
        if color_changes:
            max_change_idx = np.argmax(color_changes)
            if color_changes[max_change_idx] > np.mean(color_changes) * 2:
                return {
                    'y': max_change_idx + 10,
                    'confidence': min(color_changes[max_change_idx] / (max(color_changes) + 1e-6), 1.0),
                    'method': 'color_change'
                }
        
        return None
    
    def analyze_scale_structure(self, image: np.ndarray, water_levels: List[Dict]) -> Dict:
        """
        分析标尺结构，推断水位
        """
        if not water_levels:
            return None
        
        # 按Y坐标排序
        water_levels_sorted = sorted(water_levels, key=lambda x: x['y_position'])
        
        # 计算刻度间距
        if len(water_levels_sorted) >= 2:
            spacings = []
            value_diffs = []
            
            for i in range(len(water_levels_sorted) - 1):
                y_diff = water_levels_sorted[i+1]['y_position'] - water_levels_sorted[i]['y_position']
                value_diff = water_levels_sorted[i+1]['value'] - water_levels_sorted[i]['value']
                
                if y_diff > 0 and value_diff > 0:
                    spacings.append(y_diff)
                    value_diffs.append(value_diff)
                    
            if spacings:
                avg_pixel_per_meter = np.mean([s/v for s, v in zip(spacings, value_diffs)])
                
                return {
                    'pixel_per_meter': avg_pixel_per_meter,
                    'scale_marks': water_levels_sorted,
                    'confidence': 0.8
                }
        
        # 如果只有一个刻度，使用默认比例
        return {
            'pixel_per_meter': 100,  # 默认值
            'scale_marks': water_levels_sorted,
            'confidence': 0.5
        }
    
    def calculate_final_water_level(self, water_surface: Dict, scale_info: Dict) -> Dict:
        """
        计算最终水位值
        """
        if not water_surface or not scale_info or not scale_info['scale_marks']:
            return None
        
        water_y = water_surface['y']
        
        # 找到最接近水面的刻度
        closest_mark = min(scale_info['scale_marks'], 
                          key=lambda x: abs(x['y_position'] - water_y))
        
        # 计算偏移
        pixel_offset = water_y - closest_mark['y_position']
        
        # 转换为米
        if scale_info['pixel_per_meter'] > 0:
            meter_offset = pixel_offset / scale_info['pixel_per_meter']
        else:
            # 使用默认比例
            meter_offset = pixel_offset / 100
        
        # 计算水位（注意：图像坐标系Y向下增加，而水位向上增加）
        calculated_value = closest_mark['value'] - meter_offset
        
        return {
            'value': round(calculated_value, 2),
            'reference_mark': closest_mark['value'],
            'offset': round(meter_offset, 2),
            'confidence': min(water_surface['confidence'] * scale_info['confidence'], 1.0),
            'method': f"reference_{water_surface['method']}"
        }
    
    def process_single_image(self, image_path: str) -> Dict:
        """
        处理单张图像
        """
        print(f"\n{'='*60}")
        print(f"📷 处理图像: {image_path}")
        print(f"{'='*60}")
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            return None
        
        results = {'image_path': image_path, 'original': image}
        
        try:
            # 1. 预处理
            print("1️⃣ 图像预处理...")
            preprocess_results = self.preprocess_image(image)
            results.update(preprocess_results)
            
            # 2. 定位标尺区域
            print("2️⃣ 定位标尺区域...")
            ruler_roi, roi_bbox = self.detect_ruler_region(
                preprocess_results['resized'], 
                preprocess_results['ruler_mask']
            )
            results['ruler_roi'] = ruler_roi
            results['roi_bbox'] = roi_bbox
            
            # 3. 生成增强版本
            print("3️⃣ 生成OCR增强版本...")
            enhanced_versions = self.enhance_for_ocr(ruler_roi)
            
            # 4. 执行OCR识别
            print("4️⃣ 执行OCR识别...")
            all_ocr_results = []
            for i, enhanced in enumerate(enhanced_versions):
                ocr_results = self.perform_ocr(enhanced)
                all_ocr_results.extend(ocr_results)
            
            # 去重
            unique_texts = {}
            for result in all_ocr_results:
                text = result['text']
                if text not in unique_texts or result['confidence'] > unique_texts[text]['confidence']:
                    unique_texts[text] = result
            
            ocr_results = list(unique_texts.values())
            results['ocr_results'] = ocr_results
            
            print(f"   识别到 {len(ocr_results)} 个文本区域")
            
            # 5. 提取水位数值
            print("5️⃣ 提取水位数值...")
            water_levels = self.extract_water_level_values(ocr_results)
            results['water_levels'] = water_levels
            
            if water_levels:
                print(f"   找到 {len(water_levels)} 个可能的水位值:")
                for wl in water_levels[:3]:  # 显示前3个
                    print(f"      - {wl['value']}M (置信度: {wl['confidence']:.2f})")
            
            # 6. 检测水面
            print("6️⃣ 检测水面位置...")
            water_surface = self.detect_water_surface(ruler_roi)
            results['water_surface'] = water_surface
            
            if water_surface:
                print(f"   ✓ 检测到水面 (Y={water_surface['y']:.0f}, 方法={water_surface['method']})")
            
            # 7. 分析标尺结构
            print("7️⃣ 分析标尺结构...")
            scale_info = self.analyze_scale_structure(ruler_roi, water_levels)
            results['scale_info'] = scale_info
            
            # 8. 计算最终水位
            print("8️⃣ 计算最终水位...")
            
            # 方法A: 如果检测到水面，使用水面计算
            if water_surface and scale_info:
                final_water_level = self.calculate_final_water_level(water_surface, scale_info)
                if final_water_level:
                    results['final_water_level'] = final_water_level
                    print(f"\n✅ 水位计算成功!")
                    print(f"   🎯 水位值: {final_water_level['value']} M")
                    print(f"   📏 参考刻度: {final_water_level['reference_mark']} M")
                    print(f"   📊 置信度: {final_water_level['confidence']:.2%}")
                    return results
            
            # 方法B: 直接使用识别到的最可信水位值
            if water_levels:
                best_level = water_levels[0]  # 已按置信度排序
                results['final_water_level'] = {
                    'value': best_level['value'],
                    'confidence': best_level['confidence'],
                    'method': 'direct_ocr'
                }
                print(f"\n✅ 直接识别水位值!")
                print(f"   🎯 水位值: {best_level['value']} M")
                print(f"   📊 置信度: {best_level['confidence']:.2%}")
                return results
            
            print("❌ 未能识别到水位值")
            
        except Exception as e:
            print(f"❌ 处理出错: {e}")
            import traceback
            traceback.print_exc()
        
        return results
    
    def visualize_results(self, results: Dict):
        """
        可视化识别结果
        """
        if not results:
            return
        
        fig = plt.figure(figsize=(20, 12))
        
        # 1. 原始图像
        ax1 = plt.subplot(2, 4, 1)
        ax1.imshow(cv2.cvtColor(results['original'], cv2.COLOR_BGR2RGB))
        ax1.set_title('原始图像')
        ax1.axis('off')
        
        # 2. 标尺掩码
        ax2 = plt.subplot(2, 4, 2)
        ax2.imshow(results.get('ruler_mask', np.zeros((100,100))), cmap='gray')
        ax2.set_title('标尺检测')
        ax2.axis('off')
        
        # 3. 标尺ROI
        ax3 = plt.subplot(2, 4, 3)
        if 'ruler_roi' in results:
            ax3.imshow(cv2.cvtColor(results['ruler_roi'], cv2.COLOR_BGR2RGB))
        ax3.set_title('标尺区域')
        ax3.axis('off')
        
        # 4. OCR结果标注
        ax4 = plt.subplot(2, 4, 4)
        if 'ruler_roi' in results:
            annotated = results['ruler_roi'].copy()
            
            # 标注OCR结果
            for ocr in results.get('ocr_results', []):
                if ocr.get('bbox'):
                    bbox = np.array(ocr['bbox'], np.int32)
                    cv2.polylines(annotated, [bbox], True, (0, 255, 0), 2)
            
            # 标注水面
            if results.get('water_surface'):
                y = int(results['water_surface']['y'])
                cv2.line(annotated, (0, y), (annotated.shape[1], y), (255, 0, 0), 3)
            
            ax4.imshow(cv2.cvtColor(annotated, cv2.COLOR_BGR2RGB))
        ax4.set_title('识别结果')
        ax4.axis('off')
        
        # 5-7. 增强版本展示
        for i in range(3):
            ax = plt.subplot(2, 4, 5 + i)
            ax.imshow(results.get('resized', np.zeros((100,100,3), dtype=np.uint8))[:,:,i], cmap='gray')
            ax.set_title(f'通道 {["B","G","R"][i]}')
            ax.axis('off')
        
        # 8. 结果文本
        ax8 = plt.subplot(2, 4, 8)
        ax8.axis('off')
        
        result_text = "📊 识别结果\n" + "="*40 + "\n\n"
        
        if results.get('final_water_level'):
            wl = results['final_water_level']
            result_text += f"✅ 水位值: {wl['value']} M\n"
            result_text += f"置信度: {wl['confidence']:.2%}\n"
            result_text += f"方法: {wl.get('method', 'unknown')}\n"
            
            if wl.get('reference_mark'):
                result_text += f"参考刻度: {wl['reference_mark']} M\n"
            if wl.get('offset'):
                result_text += f"偏移量: {wl['offset']} M\n"
        else:
            result_text += "❌ 未能识别水位值\n\n"
            
            if results.get('water_levels'):
                result_text += "识别到的刻度:\n"
                for wl in results['water_levels'][:5]:
                    result_text += f"  • {wl['value']}M ({wl['confidence']:.1%})\n"
        
        ax8.text(0.1, 0.9, result_text, transform=ax8.transAxes,
                fontsize=12, verticalalignment='top', family='monospace',
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
        plt.suptitle(f"水位识别: {os.path.basename(results.get('image_path', 'unknown'))}", 
                    fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.show()
    
    def process_batch(self, image_paths: List[str]) -> List[Dict]:
        """
        批量处理多张图像
        """
        all_results = []
        
        print(f"\n{'='*60}")
        print(f"🚀 开始批量处理 {len(image_paths)} 张图像")
        print(f"{'='*60}")
        
        for i, image_path in enumerate(image_paths, 1):
            print(f"\n[{i}/{len(image_paths)}] 处理中...")
            results = self.process_single_image(image_path)
            all_results.append(results)
        
        # 汇总统计
        print(f"\n{'='*60}")
        print("📊 批量处理结果汇总")
        print(f"{'='*60}")
        
        success_count = 0
        for i, results in enumerate(all_results):
            if results and results.get('final_water_level'):
                success_count += 1
                wl = results['final_water_level']
                print(f"✅ {os.path.basename(results['image_path'])}: {wl['value']} M (置信度: {wl['confidence']:.2%})")
            else:
                print(f"❌ {os.path.basename(image_paths[i]) if i < len(image_paths) else 'unknown'}: 识别失败")
        
        print(f"\n成功率: {success_count}/{len(image_paths)} ({success_count/len(image_paths)*100:.1f}%)")
        
        return all_results


class QuickWaterLevelReader:
    """
    快速水位读取器 - 简化接口
    """
    
    def __init__(self):
        self.ocr_system = UniversalWaterLevelOCR()
    
    def read_water_level(self, image_path: str) -> float:
        """
        读取单张图像的水位值
        
        返回:
            水位值（米），如果识别失败返回None
        """
        results = self.ocr_system.process_single_image(image_path)
        
        if results and results.get('final_water_level'):
            return results['final_water_level']['value']
        
        return None
    
    def read_batch(self, image_paths: List[str]) -> Dict[str, float]:
        """
        批量读取水位值
        
        返回:
            字典，键为图像路径，值为水位值
        """
        water_levels = {}
        
        for image_path in image_paths:
            value = self.read_water_level(image_path)
            water_levels[image_path] = value
        
        return water_levels


def demo_single_image():
    """
    演示：处理单张图像
    """
    print("\n" + "🌊"*30)
    print("水位标尺OCR识别系统 - 单张图像演示")
    print("🌊"*30)
    
    # 创建识别系统
    ocr_system = UniversalWaterLevelOCR()
    
    # 处理图像
    image_path = "water.png"  # 您的图像路径
    
    if not os.path.exists(image_path):
        print(f"\n❌ 找不到图像文件: {image_path}")
        print("请将水位标尺图像保存为 'water.png'")
        return
    
    # 处理并获取结果
    results = ocr_system.process_single_image(image_path)
    
    # 可视化
    if results:
        ocr_system.visualize_results(results)
        
        # 打印最终结果
        if results.get('final_water_level'):
            wl = results['final_water_level']
            print(f"\n" + "🎯"*20)
            print(f"最终水位读数: {wl['value']} M")
            print(f"识别置信度: {wl['confidence']:.2%}")
            print("🎯"*20)


def demo_batch_processing():
    """
    演示：批量处理多张图像
    """
    print("\n" + "🌊"*30)
    print("水位标尺OCR识别系统 - 批量处理演示")
    print("🌊"*30)
    
    # 创建识别系统
    ocr_system = UniversalWaterLevelOCR()
    
    # 准备图像列表
    image_folder = "water_images"  # 图像文件夹
    
    if os.path.exists(image_folder):
        # 获取所有图像文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        image_paths = []
        
        for file in os.listdir(image_folder):
            if any(file.lower().endswith(ext) for ext in image_extensions):
                image_paths.append(os.path.join(image_folder, file))
        
        if image_paths:
            # 批量处理
            results = ocr_system.process_batch(image_paths)
            
            # 可视化每个结果
            for result in results:
                if result:
                    ocr_system.visualize_results(result)
        else:
            print(f"❌ 在 {image_folder} 中没有找到图像文件")
    else:
        # 如果没有文件夹，尝试处理单个文件
        print(f"📁 未找到 {image_folder} 文件夹，尝试处理单个文件...")
        
        # 查找当前目录下的水位图像
        test_images = ['water.png', 'water.jpg', 'water1.png', 'water2.png']
        existing_images = [img for img in test_images if os.path.exists(img)]
        
        if existing_images:
            results = ocr_system.process_batch(existing_images)
        else:
            print("❌ 未找到任何水位标尺图像")


def demo_quick_reader():
    """
    演示：使用简化接口快速读取水位
    """
    print("\n" + "🌊"*30)
    print("快速水位读取器演示")
    print("🌊"*30)
    
    # 创建快速读取器
    reader = QuickWaterLevelReader()
    
    # 读取单张图像
    image_path = "water.png"
    
    if os.path.exists(image_path):
        water_level = reader.read_water_level(image_path)
        
        if water_level:
            print(f"\n✅ 水位值: {water_level} M")
        else:
            print(f"\n❌ 无法识别水位值")
    else:
        print(f"\n❌ 找不到图像文件: {image_path}")
    
    # 批量读取示例
    print("\n批量读取示例:")
    test_images = ['water1.png', 'water2.png', 'water3.png']
    existing_images = [img for img in test_images if os.path.exists(img)]
    
    if existing_images:
        water_levels = reader.read_batch(existing_images)
        
        for path, value in water_levels.items():
            if value:
                print(f"  • {os.path.basename(path)}: {value} M")
            else:
                print(f"  • {os.path.basename(path)}: 识别失败")


def main():
    """
    主函数 - 提供交互式菜单
    """
    print("\n" + "="*60)
    print("🌊 通用水位标尺OCR识别系统 🌊")
    print("="*60)
    print("\n功能特点:")
    print("  ✓ 自动识别各种水位标尺")
    print("  ✓ 支持多种图像格式")
    print("  ✓ 批量处理能力")
    print("  ✓ 智能水面检测")
    print("  ✓ 多OCR引擎支持")
    
    print("\n请选择运行模式:")
    print("1. 处理单张图像")
    print("2. 批量处理多张图像")
    print("3. 快速读取模式")
    print("4. 运行所有演示")
    
    choice = input("\n请输入选项 (1-4): ").strip()
    
    if choice == '1':
        demo_single_image()
    elif choice == '2':
        demo_batch_processing()
    elif choice == '3':
        demo_quick_reader()
    elif choice == '4':
        demo_single_image()
        demo_batch_processing()
        demo_quick_reader()
    else:
        print("无效选项，运行默认演示...")
        demo_single_image()


if __name__ == "__main__":
    main()
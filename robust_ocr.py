#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
from paddleocr import PaddleOCR
import json

def debug_ocr_results(results):
    """调试OCR结果的数据结构"""
    print("=== 调试OCR结果数据结构 ===")
    print(f"结果类型: {type(results)}")
    print(f"结果长度: {len(results) if results else 0}")
    
    if results:
        print(f"第一层数据类型: {type(results[0])}")
        if results[0]:
            print(f"第一层数据长度: {len(results[0])}")
            if len(results[0]) > 0:
                print(f"第一个元素类型: {type(results[0][0])}")
                print(f"第一个元素内容: {results[0][0]}")

def analyze_water_level_image():
    """
    使用PaddleOCR识别水位测量图像中的数字和文本
    """
    print("=== 水位测量图像OCR识别程序 ===")
    
    # 检查图像文件
    image_path = "water.png"
    if not os.path.exists(image_path):
        print(f"错误：图像文件 {image_path} 不存在")
        return
    
    print(f"找到图像文件: {image_path}")
    print("正在初始化PaddleOCR...")
    
    try:
        # 初始化PaddleOCR
        ocr = PaddleOCR(lang='ch')
        
        print("正在进行OCR文字识别...")
        
        # 执行OCR识别
        results = ocr.ocr(image_path)
        
        # 调试结果结构
        debug_ocr_results(results)
        
        print("\n" + "="*60)
        print("OCR识别结果")
        print("="*60)
        
        if not results or not results[0]:
            print("未识别到任何文字内容")
            return
        
        # 存储识别结果
        all_texts = []
        all_numbers = []
        
        # 处理识别结果
        for idx, line in enumerate(results[0]):
            try:
                print(f"\n原始数据 {idx+1}: {line}")
                print(f"数据类型: {type(line)}")
                
                # 尝试不同的数据结构解析
                if isinstance(line, list) and len(line) >= 2:
                    bbox = line[0]
                    text_info = line[1]
                    
                    if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                        text = text_info[0]
                        confidence = text_info[1]
                    elif isinstance(text_info, str):
                        text = text_info
                        confidence = 1.0
                    else:
                        text = str(text_info)
                        confidence = 0.0
                        
                elif isinstance(line, dict):
                    text = line.get('text', str(line))
                    confidence = line.get('confidence', 0.0)
                    bbox = line.get('bbox', [])
                else:
                    text = str(line)
                    confidence = 0.0
                    bbox = []
                
                print(f"解析结果 {idx+1}:")
                print(f"  文本内容: '{text}'")
                print(f"  识别置信度: {confidence:.4f}")
                print(f"  文本位置: {bbox}")
                
                if text and len(text.strip()) > 0:
                    all_texts.append(text)
                    
                    # 提取数字
                    numbers_found = re.findall(r'\d+\.?\d*', text)
                    if numbers_found:
                        all_numbers.extend(numbers_found)
                        print(f"  包含数字: {numbers_found}")
                
                print("-" * 40)
                
            except Exception as e:
                print(f"处理第 {idx+1} 个结果时出错: {e}")
                print(f"原始数据: {line}")
                continue
        
        # 汇总分析
        print(f"\n{'='*60}")
        print("识别结果汇总分析")
        print("="*60)
        
        print(f"识别到的所有文本: {all_texts}")
        print(f"提取到的所有数字: {all_numbers}")
        
        if all_texts:
            # 合并所有文本进行整体分析
            full_text = ' '.join(all_texts)
            print(f"完整文本内容: '{full_text}'")
            
            # 分析特定模式
            print(f"\n{'='*60}")
            print("特定信息提取")
            print("="*60)
            
            # 时间戳模式
            timestamp_patterns = [
                (r'\d{4}年\d{1,2}月\d{1,2}日\s*\d{1,2}:\d{2}:\d{2}', '完整时间戳'),
                (r'\d{4}年\d{1,2}月\d{1,2}日', '日期'),
                (r'\d{1,2}:\d{2}:\d{2}', '时间'),
                (r'2025', '年份'),
                (r'09', '月份'),
                (r'31', '日期'),
                (r'11:02:03', '具体时间'),
            ]
            
            for pattern, desc in timestamp_patterns:
                matches = re.findall(pattern, full_text)
                if matches:
                    print(f"{desc}: {matches}")
            
            # 水位读数模式
            water_level_patterns = [
                (r'\d+\.?\d*\s*(?:cm|CM|厘米|毫米|mm|MM)', '带单位的测量值'),
                (r'\d+\.?\d*', '纯数字'),
            ]
            
            for pattern, desc in water_level_patterns:
                matches = re.findall(pattern, full_text)
                if matches:
                    print(f"{desc}: {matches}")
        
        print(f"\n{'='*60}")
        print("识别任务完成")
        print("="*60)
        
        return {
            'texts': all_texts,
            'numbers': all_numbers,
            'full_text': ' '.join(all_texts) if all_texts else ''
        }
        
    except Exception as e:
        print(f"OCR处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    analyze_water_level_image()
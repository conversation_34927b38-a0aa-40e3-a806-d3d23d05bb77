from paddleocr import PaddleOCR
import os

def main():
    print("=== 水位测量图像OCR识别程序 ===")
    
    # 检查图像文件
    image_path = "water.png"
    if not os.path.exists(image_path):
        print(f"错误：图像文件 {image_path} 不存在")
        return
    
    print(f"找到图像文件: {image_path}")
    print("正在初始化PaddleOCR...")
    
    # 使用最简单的初始化方式
    ocr = PaddleOCR(lang='ch')
    
    print("正在进行OCR识别...")
    
    # 进行OCR识别
    result = ocr.predict(image_path)
    
    print("\n=== OCR识别结果 ===")
    print("-" * 50)
    
    if result and len(result) > 0:
        all_numbers = []
        all_texts = []
        
        # 处理结果数据结构
        for idx, item in enumerate(result):
            if isinstance(item, dict) and 'text' in item:
                # 新版本API格式
                text = item['text']
                confidence = item.get('confidence', 0.0)
                bbox = item.get('bbox', [])
            elif isinstance(item, list) and len(item) >= 2:
                # 旧版本API格式
                bbox = item[0]
                text_info = item[1]
                if isinstance(text_info, tuple) and len(text_info) >= 2:
                    text = text_info[0]
                    confidence = text_info[1]
                else:
                    text = str(text_info)
                    confidence = 0.0
            else:
                # 处理其他格式
                text = str(item)
                confidence = 0.0
                bbox = []
            
            print(f"识别结果 {idx+1}:")
            print(f"  文本: '{text}'")
            print(f"  置信度: {confidence:.4f}")
            print(f"  位置: {bbox}")
            
            all_texts.append(text)
            
            # 提取数字
            import re
            numbers = re.findall(r'\d+\.?\d*', text)
            if numbers:
                all_numbers.extend(numbers)
                print(f"  包含数字: {numbers}")
            
            print("-" * 30)
        
        print(f"\n=== 汇总结果 ===")
        print(f"所有识别文本: {all_texts}")
        print(f"提取的数字: {all_numbers}")
        
        # 分析时间戳
        full_text = ' '.join(all_texts)
        print(f"完整文本: '{full_text}'")
        
        # 查找时间相关信息
        import re
        time_patterns = [
            r'\d{4}年\d{1,2}月\d{1,2}日',  # 日期
            r'\d{1,2}:\d{2}:\d{2}',        # 时间
            r'\d{4}年\d{1,2}月\d{1,2}日\s*\d{1,2}:\d{2}:\d{2}'  # 完整时间戳
        ]
        
        for pattern in time_patterns:
            matches = re.findall(pattern, full_text)
            if matches:
                print(f"时间信息: {matches}")
        
        print("\n=== 识别完成 ===")
        
    else:
        print("未识别到任何文字")

if __name__ == "__main__":
    main()
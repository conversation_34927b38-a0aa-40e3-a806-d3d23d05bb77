#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版水位标尺OCR - 直接识别，不做过多预处理
"""

import cv2
import numpy as np
from paddleocr import PaddleOCR
import os
import re

def recognize_water_level(image_path):
    """
    直接识别水位标尺图像
    """
    print(f"处理图像: {image_path}")
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"错误：文件不存在 - {image_path}")
        return
    
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print("错误：无法读取图像")
        return
    
    print(f"图像尺寸: {image.shape}")
    
    # 初始化PaddleOCR
    print("初始化PaddleOCR...")
    try:
        ocr = PaddleOCR(lang='ch')
    except Exception as e:
        print(f"PaddleOCR初始化失败: {e}")
        return
    
    # 直接进行OCR识别，不做预处理
    print("进行OCR识别...")
    try:
        results = ocr.ocr(image_path, cls=True)
    except Exception as e:
        print(f"OCR识别失败: {e}")
        return
    
    print("\n=== OCR识别结果 ===")
    
    if not results or not results[0]:
        print("未识别到任何文字")
        return
    
    # 存储所有识别结果
    all_texts = []
    water_levels = []
    
    # 解析识别结果
    for idx, line in enumerate(results[0]):
        if len(line) >= 2:
            bbox = line[0]
            text_info = line[1]
            
            if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                text = text_info[0]
                confidence = text_info[1]
            else:
                text = str(text_info)
                confidence = 0.0
            
            print(f"\n识别 {idx+1}:")
            print(f"  文本: '{text}'")
            print(f"  置信度: {confidence:.4f}")
            
            all_texts.append(text)
            
            # 查找水位读数模式
            # 匹配如 "381M", "379M", "378M" 等
            patterns = [
                r'(\d{3,4})\s*[Mm]',      # 数字+M，可能有空格
                r'(\d{3,4})[Mm]',         # 数字+M，无空格
                r'(\d{3,4})\s+[Mm]',      # 数字+M，有空格
                r'(\d{3,4})',             # 仅数字
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, text)
                if matches:
                    for match in matches:
                        try:
                            value = int(match)
                            # 水位通常在300-400米范围
                            if 300 <= value <= 500:
                                water_levels.append({
                                    'value': value,
                                    'unit': 'M',
                                    'text': text,
                                    'confidence': confidence
                                })
                                print(f"  ✓ 识别到水位: {value}M")
                                break
                        except:
                            pass
    
    print(f"\n=== 汇总结果 ===")
    print(f"识别到的所有文本: {all_texts}")
    print(f"\n识别到的水位读数:")
    
    if water_levels:
        # 按数值排序
        water_levels.sort(key=lambda x: x['value'], reverse=True)
        
        for level in water_levels:
            print(f"  - {level['value']}M (置信度: {level['confidence']:.4f}, 原文: '{level['text']}')")
        
        # 找出最可能的当前水位（通常是中间的值）
        if len(water_levels) >= 3:
            mid_idx = len(water_levels) // 2
            current_level = water_levels[mid_idx]
            print(f"\n推测当前水位: {current_level['value']}M")
    else:
        print("  未识别到有效的水位读数")
        print("\n可能的原因:")
        print("  1. 图像质量不清晰")
        print("  2. 文字被遮挡或模糊")
        print("  3. 需要调整图像角度或光照")
    
    # 保存识别结果的可视化
    result_image = image.copy()
    
    # 在图像上标注识别结果
    for idx, line in enumerate(results[0]):
        if len(line) >= 2:
            bbox = line[0]
            text_info = line[1]
            
            if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                text = text_info[0]
            else:
                text = str(text_info)
            
            # 绘制边界框
            pts = np.array(bbox, np.int32)
            cv2.polylines(result_image, [pts], True, (0, 255, 0), 2)
            
            # 添加文字标注
            x, y = pts[0]
            cv2.putText(result_image, text, (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 0, 0), 2)
    
    # 保存结果图像
    output_path = "ocr_result.jpg"
    cv2.imwrite(output_path, result_image)
    print(f"\n结果图像已保存为: {output_path}")

def main():
    """主函数"""
    # 尝试不同的可能图像文件
    possible_images = [
        "temp_ocr_image.jpg",
        "water.png",
        "water_level_image.jpg"
    ]
    
    image_found = False
    for image_path in possible_images:
        if os.path.exists(image_path):
            print(f"找到图像文件: {image_path}")
            recognize_water_level(image_path)
            image_found = True
            break
    
    if not image_found:
        print("未找到任何图像文件")
        print(f"请确保以下文件之一存在: {possible_images}")

if __name__ == "__main__":
    main()